<?xml version="1.0" encoding="utf-8"?>
<templates id="template_error" xml:space="preserve">

    <!-- Enhanced Error Page Template with Stacktrace Support -->
    <t t-name="error.detailed">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
                <style>
                    .code-block {
                        font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
                        line-height: 1.5;
                    }
                </style>
            </head>
            <body class="bg-gray-50 font-sans min-h-screen">
                <div class="max-w-6xl mx-auto px-6 py-8">
                    <!-- Error Header -->
                    <t t-include="error.header"/>

                    <!-- Error Message -->
                    <t t-include="error.message"/>

                    <!-- Request Information -->
                    <t t-if="request_info">
                        <t t-include="error.request_info"/>
                    </t>

                    <!-- Stacktrace -->
                    <t t-if="stacktrace and include_stacktrace">
                        <t t-include="error.stacktrace"/>
                    </t>

                    <!-- Footer -->
                    <t t-include="error.footer"/>
                </div>
            </body>
        </html>
    </t>

    <!-- Simple Error Page Template -->
    <t t-name="error.simple">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
            </head>
            <body class="bg-gray-50 font-sans min-h-screen flex items-center justify-center">
                <div class="max-w-2xl mx-auto px-6">
                    <div class="bg-white rounded-2xl shadow-xl border border-gray-200 p-12 text-center">
                        <!-- Error Icon -->
                        <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-8 text-white text-3xl shadow-lg">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>

                        <!-- Error Title -->
                        <h1 class="text-4xl font-bold text-gray-900 mb-4" t-esc="title"/>

                        <!-- Error Message -->
                        <div class="text-xl text-gray-600 mb-8 leading-relaxed" t-esc="error_message"/>

                        <!-- Actions -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="/app" class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-home mr-2"></i>
                                Go to App
                            </a>
                            <button onclick="history.back()" class="inline-flex items-center justify-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Go Back
                            </button>
                        </div>

                        <!-- Footer -->
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <p class="text-sm text-gray-500">ERP System</p>
                        </div>
                    </div>
                </div>
            </body>
        </html>
    </t>

    <!-- Error Header Component -->
    <t t-name="error.header">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
            <div class="flex items-start space-x-6">
                <!-- Error Icon -->
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white text-2xl shadow-lg">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>

                <!-- Error Details -->
                <div class="flex-1 min-w-0">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2" t-esc="title"/>
                    <p class="text-lg text-gray-600 mb-4" t-esc="error_type"/>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            <span t-esc="timestamp"/>
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-code mr-1"></i>
                            Status <span t-esc="status_code" class="ml-1 font-semibold"/>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Error Message Component -->
    <t t-name="error.message">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                Error Details
            </h2>
            <div class="bg-red-50 border-l-4 border-red-400 p-6 rounded-r-lg">
                <div class="text-red-800 font-medium text-lg leading-relaxed" t-esc="error_message"/>
            </div>
        </div>
    </t>

    <!-- Request Information Component -->
    <t t-name="error.request_info">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-globe mr-2 text-green-500"></i>
                Request Information
            </h2>
            <div class="bg-gray-50 rounded-xl p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <t t-foreach="request_info.items()" t-as="item">
                        <div class="flex flex-col space-y-1">
                            <dt class="text-sm font-semibold text-gray-700" t-esc="item[0]"/>
                            <dd class="text-sm text-gray-900 font-mono bg-white px-3 py-2 rounded border" t-esc="item[1]"/>
                        </div>
                    </t>
                </div>
            </div>
        </div>
    </t>

    <!-- Stacktrace Component -->
    <t t-name="error.stacktrace">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-bug mr-2 text-orange-500"></i>
                Stack Trace
            </h2>
            <div class="bg-gray-900 rounded-xl p-6 overflow-x-auto">
                <pre class="code-block text-green-400 text-sm leading-relaxed whitespace-pre-wrap" t-esc="stacktrace"/>
            </div>
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-3"></i>
                    <div class="text-sm text-yellow-800">
                        <p class="font-semibold mb-1">Development Mode</p>
                        <p>This detailed error information is shown because the application is running in development mode. In production, users would see a simplified error message.</p>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Footer Component -->
    <t t-name="error.footer">
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div class="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0">
                <!-- Actions -->
                <div class="flex flex-wrap gap-3">
                    <a href="/app" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-home mr-2"></i>
                        Go to App
                    </a>
                    <a href="/app/databases" class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-database mr-2"></i>
                        Database List
                    </a>
                    <button onclick="history.back()" class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Go Back
                    </button>
                    <button onclick="window.location.reload()" class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        <i class="fas fa-redo mr-2"></i>
                        Retry
                    </button>
                </div>

                <!-- System Info -->
                <div class="text-center lg:text-right">
                    <p class="text-sm text-gray-500">ERP System</p>
                    <p class="text-xs text-gray-400" t-esc="timestamp"/>
                </div>
            </div>
        </div>
    </t>

    <!-- Legacy Error Page Template (for backward compatibility) -->
    <t t-name="error.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
            </head>
            <body class="font-sans m-0 p-5 bg-gray-100 min-h-screen">
                <div class="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-lg">
                    <div class="text-center mb-8 pb-5 border-b-2 border-red-100">
                        <h1 class="text-red-700 m-0 text-4xl font-bold" t-esc="title"/>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded p-5 my-5">
                        <div class="text-red-700 font-medium mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            An error occurred while processing your request.
                        </div>
                        <t t-if="error_message">
                            <div class="text-gray-600 font-mono bg-gray-100 p-3 rounded mt-3 break-all text-sm" t-esc="error_message"/>
                        </t>
                    </div>

                    <div class="text-center mt-8">
                        <a href="/app" class="inline-block px-5 py-3 mx-2 no-underline rounded font-medium transition-colors duration-300 bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-home mr-2"></i>
                            Go to App
                        </a>
                        <a href="/app/databases" class="inline-block px-5 py-3 mx-2 no-underline rounded font-medium transition-colors duration-300 bg-gray-600 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            <i class="fas fa-database mr-2"></i>
                            Database List
                        </a>
                        <button onclick="history.back()" class="inline-block px-5 py-3 mx-2 border-0 rounded font-medium transition-colors duration-300 bg-gray-600 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 cursor-pointer">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </body>
        </html>
    </t>

</templates>
